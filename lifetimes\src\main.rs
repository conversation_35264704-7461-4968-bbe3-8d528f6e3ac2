fn next_language<'a>(languages: &'a [String], current: &str) -> &'a str {
    let mut found = false;

    for lang in languages {
        if found {
            return lang;
        }

        if lang == current {
            found = true;
        }
    }

    languages.last().unwrap()
}

fn last_language(languages: &[String]) -> &str {
    languages.last().unwrap()
}

fn longest_language<'a>(lang_a: &'a str, lang_b: &'a str) -> &'a str {
    if lang_a.len() >= lang_b.len() { lang_a } else { lang_b }
}

fn longest_in_vector(strings: &[String]) -> &str {
    strings
        .iter()
        .map(|s| s.as_str())
        .max_by_key(|s| s.len())
        .unwrap_or("")
}

fn main() {
    let languages = vec![String::from("Rust"), String::from("Go"), String::from("Typescript")];

    let result = next_language(&languages, "Rust");
    println!("The next language is {}", result);

    let result = last_language(&languages);
    println!("The last language is {}", result);

    let result = longest_language("Rust", "Go");
    println!("The longest language is {}", result);

    let result = longest_in_vector(&languages);
    println!("The longest language is {}", result);
}
