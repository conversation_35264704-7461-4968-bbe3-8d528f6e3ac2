use rand::{rng, seq::SliceRandom};

#[derive(Debug)]
struct Deck {
    cards: Vec<String>,
}

impl Deck {
    fn new() -> Self {
        let suits = ["Hearts", "Diamonds", "Clubs", "Spades"];
        let values = ["Ace", "Two", "Three", "Four", "Five", "Six", "Seven", "Eight", "Nine", "Ten", "Jack", "Queen", "King"];

        let mut cards = vec![];

        for suit in suits {
            for value in values {
                let card = format!("{} of {}", value, suit);
                cards.push(card);
            }
        }

        Deck { cards }
    }
    fn shuffle(&mut self) {
        let mut rng = rng();
        self.cards.shuffle(&mut rng);
    }
    fn deal(&mut self, num_cards: usize) -> Option<Vec<String>> {
        if num_cards > self.cards.len() {
            None // Return None if trying to deal more cards than available
        } else {
            Some(self.cards.split_off(self.cards.len() - num_cards))
        }
    }
}

fn main() {
    let mut deck = Deck::new();
    deck.shuffle();
    let cards = deck.deal(3);
    if let Some(cards) = cards {
        println!("Here's your hand: {:#?}", cards);
    } else {
        println!("Not enough cards in the deck.");
    }

    // if let None = cards {
    //     println!("Not enough cards in the deck.");
    // } else {
    //     println!("Here's your hand: {:#?}", cards);
    // }
    println!("Here's your deck: {:#?}", deck);
}
