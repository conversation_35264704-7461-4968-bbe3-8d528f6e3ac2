mod content;

use content::media::Media;
use content::catalog::Catalog;

fn main() {
    let audiobook = Media::AudioBook {
        title: String::from("An Audiobook"),
    };
    let good_movie = Media::Movie {
        title: String::from("Good movie"),
        director: String::from("Good director"),
    };
    let bad_book = Media::Book {
        title: String::from("Bad book"),
        author: String::from("Bad author"),
    };
    let postcast = Media::Postcast(10);
    let placeholder = Media::Placeholder;

    let mut catalog = Catalog::new();

    catalog.add(audiobook);
    catalog.add(good_movie);
    catalog.add(bad_book);
    catalog.add(postcast);
    catalog.add(placeholder);

    let item = catalog.get_by_index(10);
    let placeholder = Media::Placeholder;
    println!("Item {:#?}", item.unwrap_or(&placeholder));

    // match catalog.get_by_index(90) {
    //     MightHaveAValue::ThereIsAValue(value) => {
    //         println!("Item {:#?}", value);
    //     }
    //     MightHaveAValue::NoValueAvailable => {
    //         println!("No value available");
    //     }
    // }

    // if let Some(value) = catalog.get_by_index(9) {
    //     println!("Item {:#?}", value);
    // } else {
    //     println!("No value available");
    // }
}
