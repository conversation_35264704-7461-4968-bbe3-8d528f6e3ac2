use std::net::UdpSocket;
use std::io;

fn main() -> io::Result<()> {
    // Bind to all interfaces on port 8080 (better for WSL)
    let socket = UdpSocket::bind("0.0.0.0:8080")?;
    println!("UDP server listening on 0.0.0.0:8080");

    // Use a larger buffer - UDP max theoretical size is 65507 bytes
    // In practice, most networks limit to ~1500 bytes (MTU)
    let mut buf = [0; 65507];

    loop {
        // Receive data from client
        match socket.recv_from(&mut buf) {
            Ok((size, src)) => {
                println!(
                    "Received {} bytes from {}: {}",
                    size,
                    src,
                    String::from_utf8_lossy(&buf[0..size])
                );

                // Check if we might have truncated data
                if size == buf.len() {
                    println!("Warning: Received data may have been truncated!");
                }

                // Echo the message back to the sender
                let response = format!("Echo: {}", String::from_utf8_lossy(&buf[0..size]));
                socket.send_to(response.as_bytes(), src)?;
            }
            Err(e) => {
                eprintln!("Error receiving data: {}", e);
            }
        }
    }
}
