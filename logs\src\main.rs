use std::fs;
use std::io::Error;

fn extract_errors(text: &str) -> Vec<&str> {
    let split_text = text.split("\n");
    let mut results = vec![];

    for line in split_text {
        if line.contains("ERROR") {
            results.push(line);
        }
    }

    results
}

fn main() -> Result<(), Error> {
    let text = fs::read_to_string("logs.txt")?;
    let error_logs = extract_errors(text.as_str());
    fs::write("errors.txt", error_logs.join("\n"))?;

    Ok(())

    // let text = fs::read_to_string("logs.txt").expect("Failed to read file");
    // let error_logs = extract_errors(text.as_str());
    // fs::write("errors.txt", error_logs.join("\n")).expect("Failed to write file");

    // match fs::read_to_string("logs.txt") {
    //     Ok(text) => {
    //         let error_logs = extract_errors(&text);
    //         match fs::write("errors.txt", error_logs.join("\n")) {
    //             Ok(_) => {
    //                 println!("Successfully wrote errors to file");
    //             }
    //             Err(error) => {
    //                 println!("Error: failed to write file: {}", error);
    //             }
    //         }
    //     }
    //     Err(error) => {
    //         println!("Error: failed to read file: {}", error);
    //     }
    // }
}
