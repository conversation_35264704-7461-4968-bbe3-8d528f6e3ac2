use super::media::Media;

#[derive(Debug)]
pub struct Catalog {
    media: Vec<Media>,
}

impl Catalog {
    pub fn new() -> Self {
        Catalog { media: vec![] }
    }

    pub fn add(&mut self, media: Media) {
        self.media.push(media);
    }
    pub fn get_by_index(&self, index: usize) -> Option<&Media> {
        if self.media.len() > index { Some(&self.media[index]) } else { None }
    }
}
