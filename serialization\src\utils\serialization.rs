use bincode::{ config, Decode, Encode };

/// Encode using standard configuration
pub fn encode_message<T: Encode>(value: T) -> Result<Vec<u8>, bincode::error::EncodeError> {
    bincode::encode_to_vec(value, config::standard())
}

/// Decode using standard configuration
pub fn decode_message<T: Decode<()>>(bytes: &[u8]) -> Result<T, bincode::error::DecodeError> {
    bincode::decode_from_slice(bytes, config::standard()).map(|(value, _)| value)
}

/// Decode and return bytes consumed
pub fn decode_with_len<T: Decode<()>>(
    bytes: &[u8]
) -> Result<(T, usize), bincode::error::DecodeError> {
    bincode::decode_from_slice(bytes, config::standard())
}
