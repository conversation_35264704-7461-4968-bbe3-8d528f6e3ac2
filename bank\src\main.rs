#[derive(Debug)]
struct Bank {
    accounts: Vec<Account>,
}
#[derive(Debug)]
struct Account {
    id: u32,
    balance: i32,
    holder: String,
    transactions: Vec<transaction>,
}

#[derive(Debug)]
struct transaction {
    from: u32,
    to: u32,
    amount: i32,
}

impl Account {
    fn new(id: u32, holder: String) -> Self {
        Account {
            id,
            holder,
            balance: 0,
            transactions: vec![],
        }
    }
    fn set_account(&mut self, id: u32, holder: String, balance: i32) {
        self.id = id;
        self.holder = holder;
        self.balance = balance;
    }
    fn get_account(&self) -> &Account {
        self
    }
    fn set_id(&mut self, id: u32) {
        self.id = id;
    }
    fn get_id(&self) -> u32 {
        self.id
    }
    fn set_holder(&mut self, holder: String) {
        self.holder = holder;
    }
    fn get_holder(&self) -> &str {
        &self.holder
    }
    fn set_balance(&mut self, balance: i32) {
        self.balance = balance;
    }
    fn deposit(&mut self, amount: i32) {
        self.balance += amount;
    }
    fn withdraw(&mut self, amount: i32) {
        self.balance -= amount;
    }
    fn get_balance(&self) -> i32 {
        self.balance
    }
}

impl Bank {
    fn new() -> Self {
        Bank { accounts: vec![] }
    }
    fn get_number_of_accounts(&self) -> usize {
        self.accounts.len()
    }
    fn find_by_id(&self, id: u32) -> Option<&Account> {
        match self.accounts.iter().find(|account| account.id == id) {
            Some(account) => Some(account),
            None => None,
        }
    }
    fn find_by_holder(&self, holder: &str) -> Option<&Account> {
        match self.accounts.iter().find(|account| account.holder == holder) {
            Some(account) => Some(account),
            None => None,
        }
    }
}

fn main() {
    let mut bank = Bank::new();
    let mut account = Account::new(1, String::from("Daniel"));
    let mut account2 = Account::new(2, String::from("John"));
    let mut account3 = Account::new(3, String::from("Jane"));

    let mut transaction = transaction {
        from: 1,
        to: 2,
        amount: 1000,
    };

    account.set_balance(1000);
    account.transactions.push(transaction);
    account2.set_balance(2000);
    bank.accounts.push(account);
    bank.accounts.push(account2);

    bank.accounts.iter_mut().for_each(|account| {
        account.deposit(1000);
    });

    let account = bank.find_by_id(1);
    if let Some(account) = account {
        println!("Account: {:?}", account);
    } else {
        println!("Account not found");
    }

    let account = bank.find_by_holder("John");
    if let Some(account) = account {
        println!("Account: {:?}", account);
    } else {
        println!("Account not found");
    }

    // println!("{:#?}", bank);
    println!("Number of accounts: {}", bank.get_number_of_accounts());
}
