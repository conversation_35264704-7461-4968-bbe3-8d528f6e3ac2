use bincode::{ Decode, Encode };
use serialization::utils::serialization::{ encode_message, decode_message };

#[derive(Debug, Encode, Decode)]
pub enum ServerMessage {
    PlayerMove {
        entity: u64,
        position: [f32; 3],
        data: String,
    },
}

pub struct Test {}

fn main() {
    let message = ServerMessage::PlayerMove {
        entity: 123,
        position: [1.0, 2.0, 3.0],
        data: "Hello there, this is really long".to_string(),
    };
    println!("Original: {:?}", message);

    // Now you can use the direct functions
    let bytes = encode_message(message).unwrap();
    println!("bytes: {:?}", bytes);

    let decoded: ServerMessage = decode_message(&bytes).unwrap();

    println!("Decoded: {:?}", decoded);

    match decoded {
        ServerMessage::PlayerMove { entity, position, data } => {
            // Access the fields directly
            println!("Entity ID: {}", entity);
            println!("Position: {:?}", position);
            println!("Data: {:?}", data);
        }
        // Handle other variants...
    }
}
